backend:
  name: github
  repo: lightquanttrading/lightquant-app # Replace with your TARGET blog repository name
  branch: main # Branch to update (optional; defaults to master)
  # Optional: Enable GraphQL API for better performance (not compatible with git-gateway)
  # use_graphql: true

# For local development, you can use test-repo backend:
# backend:
#   name: test-repo

# This line should *not* be indented
publish_mode: editorial_workflow

# This line should *not* be indented
media_folder: "public/images/uploads" # Media files will be stored in the TARGET repo
public_folder: "/images/uploads" # The src attribute for uploaded media will begin with /images/uploads

collections:
  - name: "blog" # Used in routes, e.g., /admin/collections/blog
    label: "Blog" # Used in the UI
    folder: "content/posts" # Creates posts in your target blog repo's content/posts folder
    create: true # Allow users to create new documents in this collection
    slug: "{{year}}-{{month}}-{{day}}-{{slug}}" # Filename template, e.g., YYYY-MM-DD-title.md
    fields: # The fields for each document, usually in front matter
      - {label: "Title", name: "title", widget: "string"}
      - {label: "Slug", name: "slug", widget: "string"}
      - {label: "Publish Date", name: "date", widget: "datetime"}
      - {label: "Featured Image", name: "thumbnail", widget: "image", required: false}
      - {label: "Author", name: "author", widget: "string"}
      - {label: "Read Time", name: "readTime", widget: "string", required: false}
      - {label: "Body", name: "body", widget: "markdown"}
      - label: "Tags"
        name: "tags"
        widget: "list"
        required: false
        allow_add: true
      - {label: "Draft", name: "draft", widget: "boolean", default: false}
      - {label: "Vietnamese Blog", name: "isVietnamese", widget: "boolean", default: false, hint: "Toggle on for Vietnamese content, off for English content"}
  
  - name: "pages" # Used in routes, e.g., /admin/collections/pages
    label: "Pages" # Used in the UI
    folder: "content/pages" # The path to the folder where the documents are stored
    create: true # Allow users to create new documents in this collection
    slug: "{{slug}}" # Filename template
    fields: # The fields for each document, usually in front matter
      - {label: "Title", name: "title", widget: "string"}
      - {label: "Slug", name: "slug", widget: "string"}
      - {label: "Body", name: "body", widget: "markdown"}
      - {label: "Draft", name: "draft", widget: "boolean", default: false}
      - {label: "Vietnamese Page", name: "isVietnamese", widget: "boolean", default: false, hint: "Toggle on for Vietnamese content, off for English content"}
